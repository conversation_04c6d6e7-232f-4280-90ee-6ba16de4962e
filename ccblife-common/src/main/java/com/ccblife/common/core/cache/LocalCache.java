package com.ccblife.common.core.cache;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.HashSet;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import org.springframework.stereotype.Component;

/**
 * 本地缓存工具类
 *
 * <AUTHOR>
 **/
@SuppressWarnings(value = { "unchecked", "rawtypes" })
@Component
public class LocalCache
{
    // 缓存存储
    private final Map<String, CacheItem> cache = new ConcurrentHashMap<>();
    
    // 定时清理过期缓存的线程池
    private ScheduledExecutorService scheduler;
    
    // 缓存项内部类
    private static class CacheItem {
        private Object value;
        private long expireTime;
        
        public CacheItem(Object value, long expireTime) {
            this.value = value;
            this.expireTime = expireTime;
        }
        
        public Object getValue() {
            return value;
        }
        
        public boolean isExpired() {
            return expireTime > 0 && System.currentTimeMillis() > expireTime;
        }
    }
    
    @PostConstruct
    public void init() {
        // 启动定时清理任务，每分钟清理一次过期缓存
        scheduler = Executors.newScheduledThreadPool(1);
        scheduler.scheduleAtFixedRate(this::cleanExpiredCache, 60, 60, TimeUnit.SECONDS);
    }
    
    @PreDestroy
    public void destroy() {
        if (scheduler != null) {
            scheduler.shutdown();
        }
    }
    
    // 清理过期缓存
    private void cleanExpiredCache() {
        cache.entrySet().removeIf(entry -> entry.getValue().isExpired());
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key 缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value)
    {
        cache.put(key, new CacheItem(value, -1)); // -1表示永不过期
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key 缓存的键值
     * @param value 缓存的值
     * @param timeout 时间
     * @param timeUnit 时间颗粒度
     */
    public <T> void setCacheObject(final String key, final T value, final Integer timeout, final TimeUnit timeUnit)
    {
        long expireTime = System.currentTimeMillis() + timeUnit.toMillis(timeout);
        cache.put(key, new CacheItem(value, expireTime));
    }

    /**
     * 设置有效时间
     *
     * @param key 缓存键
     * @param timeout 超时时间
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout)
    {
        return expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置有效时间
     *
     * @param key 缓存键
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout, final TimeUnit unit)
    {
        CacheItem item = cache.get(key);
        if (item != null) {
            long expireTime = System.currentTimeMillis() + unit.toMillis(timeout);
            cache.put(key, new CacheItem(item.getValue(), expireTime));
            return true;
        }
        return false;
    }

    /**
     * 获取有效时间
     *
     * @param key 缓存键
     * @return 有效时间(秒)
     */
    public long getExpire(final String key)
    {
        CacheItem item = cache.get(key);
        if (item != null && item.expireTime > 0) {
            long remaining = item.expireTime - System.currentTimeMillis();
            return remaining > 0 ? remaining / 1000 : -1;
        }
        return -1;
    }

    /**
     * 判断 key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public Boolean hasKey(String key)
    {
        CacheItem item = cache.get(key);
        return item != null && !item.isExpired();
    }

    /**
     * 获得缓存的基本对象。
     *
     * @param key 缓存键值
     * @return 缓存键值对应的数据
     */
    public <T> T getCacheObject(final String key)
    {
        CacheItem item = cache.get(key);
        if (item != null && !item.isExpired()) {
            return (T) item.getValue();
        }
        // 如果过期，删除该缓存项
        if (item != null && item.isExpired()) {
            cache.remove(key);
        }
        return null;
    }

    /**
     * 删除单个对象
     *
     * @param key
     */
    public boolean deleteObject(final String key)
    {
        return cache.remove(key) != null;
    }

    /**
     * 删除集合对象
     *
     * @param collection 多个对象
     * @return
     */
    public boolean deleteObject(final Collection collection)
    {
        if (collection == null || collection.isEmpty()) {
            return false;
        }
        int count = 0;
        for (Object key : collection) {
            if (cache.remove(String.valueOf(key)) != null) {
                count++;
            }
        }
        return count > 0;
    }

    /**
     * 缓存List数据
     *
     * @param key 缓存的键值
     * @param dataList 待缓存的List数据
     * @return 缓存的对象
     */
    public <T> long setCacheList(final String key, final List<T> dataList)
    {
        cache.put(key, new CacheItem(new ArrayList<>(dataList), -1));
        return dataList.size();
    }

    /**
     * 获得缓存的list对象
     *
     * @param key 缓存的键值
     * @return 缓存键值对应的数据
     */
    public <T> List<T> getCacheList(final String key)
    {
        CacheItem item = cache.get(key);
        if (item != null && !item.isExpired()) {
            Object value = item.getValue();
            if (value instanceof List) {
                return (List<T>) value;
            }
        }
        return new ArrayList<>();
    }

    /**
     * 缓存Set
     *
     * @param key 缓存键值
     * @param dataSet 缓存的数据
     * @return 缓存数据的对象
     */
    public <T> Set<T> setCacheSet(final String key, final Set<T> dataSet)
    {
        Set<T> newSet = new HashSet<>(dataSet);
        cache.put(key, new CacheItem(newSet, -1));
        return newSet;
    }

    /**
     * 获得缓存的set
     *
     * @param key
     * @return
     */
    public <T> Set<T> getCacheSet(final String key)
    {
        CacheItem item = cache.get(key);
        if (item != null && !item.isExpired()) {
            Object value = item.getValue();
            if (value instanceof Set) {
                return (Set<T>) value;
            }
        }
        return new HashSet<>();
    }

    /**
     * 缓存Map
     *
     * @param key
     * @param dataMap
     */
    public <T> void setCacheMap(final String key, final Map<String, T> dataMap)
    {
        if (dataMap != null) {
            cache.put(key, new CacheItem(new ConcurrentHashMap<>(dataMap), -1));
        }
    }

    /**
     * 获得缓存的Map
     *
     * @param key
     * @return
     */
    public <T> Map<String, T> getCacheMap(final String key)
    {
        CacheItem item = cache.get(key);
        if (item != null && !item.isExpired()) {
            Object value = item.getValue();
            if (value instanceof Map) {
                return (Map<String, T>) value;
            }
        }
        return new ConcurrentHashMap<>();
    }

    /**
     * 往Hash中存入数据
     *
     * @param key 缓存键
     * @param hKey Hash键
     * @param value 值
     */
    public <T> void setCacheMapValue(final String key, final String hKey, final T value)
    {
        CacheItem item = cache.get(key);
        Map<String, T> map;
        if (item != null && !item.isExpired() && item.getValue() instanceof Map) {
            map = (Map<String, T>) item.getValue();
        } else {
            map = new ConcurrentHashMap<>();
            cache.put(key, new CacheItem(map, -1));
        }
        map.put(hKey, value);
    }

    /**
     * 获取Hash中的数据
     *
     * @param key 缓存键
     * @param hKey Hash键
     * @return Hash中的对象
     */
    public <T> T getCacheMapValue(final String key, final String hKey)
    {
        CacheItem item = cache.get(key);
        if (item != null && !item.isExpired() && item.getValue() instanceof Map) {
            Map<String, T> map = (Map<String, T>) item.getValue();
            return map.get(hKey);
        }
        return null;
    }

    /**
     * 获取多个Hash中的数据
     *
     * @param key 缓存键
     * @param hKeys Hash键集合
     * @return Hash对象集合
     */
    public <T> List<T> getMultiCacheMapValue(final String key, final Collection<Object> hKeys)
    {
        CacheItem item = cache.get(key);
        if (item != null && !item.isExpired() && item.getValue() instanceof Map) {
            Map<String, T> map = (Map<String, T>) item.getValue();
            return hKeys.stream()
                    .map(hKey -> map.get(String.valueOf(hKey)))
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 删除Hash中的某条数据
     *
     * @param key 缓存键
     * @param hKey Hash键
     * @return 是否成功
     */
    public boolean deleteCacheMapValue(final String key, final String hKey)
    {
        CacheItem item = cache.get(key);
        if (item != null && !item.isExpired() && item.getValue() instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) item.getValue();
            return map.remove(hKey) != null;
        }
        return false;
    }

    /**
     * 获得缓存的基本对象列表
     *
     * @param pattern 字符串前缀
     * @return 对象列表
     */
    public Collection<String> keys(final String pattern)
    {
        try {
            // 转义特殊字符，然后替换*为.*
            String escapedPattern = pattern.replaceAll("([\\[\\](){}.*+?^$|\\\\])", "\\\\$1");
            String regex = escapedPattern.replace("\\*", ".*");
            return cache.keySet().stream()
                    .filter(key -> key.matches(regex))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            // 如果正则表达式出错，使用简单的前缀匹配
            String prefix = pattern.replace("*", "");
            return cache.keySet().stream()
                    .filter(key -> key.startsWith(prefix))
                    .collect(Collectors.toList());
        }
    }
}
