package com.ccblife;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 启动程序
 * 
 * <AUTHOR>
 */
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
public class CcbLifeApplication
{
    public static void main(String[] args)
    {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(CcbLifeApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  CcbLife启动成功   ლ(´ڡ`ლ)ﾞ  \n");
    }
}
