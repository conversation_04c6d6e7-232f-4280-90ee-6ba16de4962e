services:
  # PostgreSQL Database
  postgres:
    image: postgres:13-alpine
    container_name: ccblife-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-ccblife}
      POSTGRES_USER: ${POSTGRES_USER:-ccblife}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-ccblife123}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql:/docker-entrypoint-initdb.d
    networks:
      - ccblife-internal
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-ccblife}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # CcbLife Backend Application
  ccblife-app:
    build:
      context: .
      dockerfile: Dockerfile
    image: ccblife-server:latest
    container_name: ccblife-server
    restart: unless-stopped
    ports:
      - "${APP_PORT:-8090}:8090"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: *******************************/${POSTGRES_DB:-ccblife}?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai
      SPRING_DATASOURCE_USERNAME: ${POSTGRES_USER:-ccblife}
      SPRING_DATASOURCE_PASSWORD: ${POSTGRES_PASSWORD:-ccblife123}
      CCBLIFE_PROFILE: ${CCBLIFE_PROFILE:-prod}
      SERVER_PORT: 8090
      JAVA_OPTS: "-Duser.timezone=Asia/Shanghai -Dfile.encoding=UTF-8 -Djava.awt.headless=true -Xms512m -Xmx1024m"
    volumes:
      - app_logs:/app/logs
      - app_uploads:/app/uploadPath
    networks:
      - ccblife-internal
    depends_on:
      postgres:
        condition: service_healthy

volumes:
  postgres_data:
    driver: local
  app_logs:
    driver: local
  app_uploads:
    driver: local

networks:
  ccblife-internal:
    driver: bridge
