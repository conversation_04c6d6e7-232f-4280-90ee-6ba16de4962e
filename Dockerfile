# 使用OpenJDK 8作为基础镜像
FROM openjdk:8-jre-alpine

# 设置维护者信息
LABEL maintainer="<EMAIL>"
LABEL description="CcbLife自动化测试管理系统"
LABEL version="3.8.8"

# 设置工作目录
WORKDIR /app

# 安装必要的包、字体和设置时区
RUN apk add --no-cache \
    tzdata \
    fontconfig \
    ttf-dejavu \
    curl \
    && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && fc-cache -fv

# 创建应用用户
RUN addgroup -g 1000 ccblife && \
    adduser -D -s /bin/sh -u 1000 -G ccblife ccblife

# 创建必要的目录
RUN mkdir -p /app/logs /app/uploadPath && \
    chown -R ccblife:ccblife /app

# 复制jar文件
COPY ccblife-admin/target/ccblife-admin.jar /app/ccblife-admin.jar

# 设置文件权限
RUN chown ccblife:ccblife /app/ccblife-admin.jar

# 切换到应用用户
USER ccblife

# 暴露端口
EXPOSE 8090

# 启动命令
ENTRYPOINT ["java", "-Duser.timezone=Asia/Shanghai", "-Dfile.encoding=UTF-8", "-jar", "/app/ccblife-admin.jar"]
