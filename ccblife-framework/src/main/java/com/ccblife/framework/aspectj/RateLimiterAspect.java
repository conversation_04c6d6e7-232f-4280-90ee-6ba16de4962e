package com.ccblife.framework.aspectj;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ccblife.common.annotation.RateLimiter;
import com.ccblife.common.enums.LimitType;
import com.ccblife.common.exception.ServiceException;
import com.ccblife.common.utils.StringUtils;
import com.ccblife.common.utils.ip.IpUtils;
import com.ccblife.framework.config.LocalCacheConfig.RateLimitInfo;

/**
 * 限流处理 (使用本地缓存实现)
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class RateLimiterAspect
{
    private static final Logger log = LoggerFactory.getLogger(RateLimiterAspect.class);

    @Autowired
    private Map<String, RateLimitInfo> rateLimitStorage;

    @Before("@annotation(rateLimiter)")
    public void doBefore(JoinPoint point, RateLimiter rateLimiter) throws Throwable
    {
        int time = rateLimiter.time();
        int count = rateLimiter.count();

        String combineKey = getCombineKey(rateLimiter, point);
        try
        {
            long currentCount = executeRateLimit(combineKey, count, time);
            if (currentCount > count)
            {
                throw new ServiceException("访问过于频繁，请稍候再试");
            }
            log.info("限制请求'{}',当前请求'{}',缓存key'{}'", count, currentCount, combineKey);
        }
        catch (ServiceException e)
        {
            throw e;
        }
        catch (Exception e)
        {
            throw new RuntimeException("服务器限流异常，请稍候再试");
        }
    }

    /**
     * 执行限流逻辑
     */
    private long executeRateLimit(String key, int count, int time) {
        RateLimitInfo rateLimitInfo = rateLimitStorage.get(key);
        long currentTime = System.currentTimeMillis();

        // 如果不存在或已过期，创建新的限流信息
        if (rateLimitInfo == null || rateLimitInfo.isExpired()) {
            long expireTime = currentTime + TimeUnit.SECONDS.toMillis(time);
            rateLimitInfo = new RateLimitInfo(1, expireTime);
            rateLimitStorage.put(key, rateLimitInfo);
            return 1;
        }

        // 增加计数并返回当前计数
        return rateLimitInfo.incrementAndGet();
    }

    public String getCombineKey(RateLimiter rateLimiter, JoinPoint point)
    {
        StringBuffer stringBuffer = new StringBuffer(rateLimiter.key());
        if (rateLimiter.limitType() == LimitType.IP)
        {
            stringBuffer.append(IpUtils.getIpAddr()).append("-");
        }
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        Class<?> targetClass = method.getDeclaringClass();
        stringBuffer.append(targetClass.getName()).append("-").append(method.getName());
        return stringBuffer.toString();
    }
}
