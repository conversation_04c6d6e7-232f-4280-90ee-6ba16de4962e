package com.ccblife.framework.config;

import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.Map;

/**
 * 本地缓存配置
 * 
 * <AUTHOR>
 */
@Configuration
@EnableCaching
public class LocalCacheConfig extends CachingConfigurerSupport
{
    /**
     * 限流计数器存储
     */
    @Bean
    public Map<String, RateLimitInfo> rateLimitStorage()
    {
        return new ConcurrentHashMap<>();
    }

    /**
     * 限流信息内部类
     */
    public static class RateLimitInfo {
        private AtomicLong count;
        private long expireTime;
        
        public RateLimitInfo(long count, long expireTime) {
            this.count = new AtomicLong(count);
            this.expireTime = expireTime;
        }
        
        public long incrementAndGet() {
            return count.incrementAndGet();
        }
        
        public long get() {
            return count.get();
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() > expireTime;
        }
        
        public long getExpireTime() {
            return expireTime;
        }
    }
}
