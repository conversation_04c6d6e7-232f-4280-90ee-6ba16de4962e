@echo off

rem CcbLife Backend Service Management Script for Windows
rem For local development and testing

rem jar file directory
set AppName=ccblife-admin.jar

rem JVM parameters
set JVM_OPTS=-Dname=%AppName% -Duser.timezone=Asia/Shanghai -Djava.awt.headless=true -Djava.security.egd=file:/dev/./urandom -Dfile.encoding=UTF-8 -Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -XX:+PrintGCDateStamps -XX:+PrintGCDetails -XX:NewRatio=1 -XX:SurvivorRatio=30 -XX:+UseParallelGC -XX:+UseParallelOldGC

:menu
ECHO.
ECHO.  [1] Start %AppName%
ECHO.  [2] Stop %AppName%
ECHO.  [3] Restart %AppName%
ECHO.  [4] Check Status %AppName%
ECHO.  [5] Exit
ECHO.

ECHO.Please select option:
set /p ID=
IF "%ID%"=="1" GOTO start
IF "%ID%"=="2" GOTO stop
IF "%ID%"=="3" GOTO restart
IF "%ID%"=="4" GOTO status
IF "%ID%"=="5" EXIT
ECHO Invalid choice, please try again
GOTO menu
:start
echo Checking if %AppName% is already running...
set pid=
for /f "tokens=1,2" %%a in ('jps -l 2^>nul ^| findstr %AppName%') do (
    set pid=%%a
    set image_name=%%b
)
if defined pid (
    echo %AppName% is already running with PID: %pid%
    PAUSE
    GOTO menu
)

echo Starting %AppName%...
start javaw %JVM_OPTS% -jar %AppName%
echo Start command executed...
echo %AppName% started successfully!
PAUSE
GOTO menu

:stop
echo Stopping %AppName%...
set pid=
for /f "tokens=1,2" %%a in ('jps -l 2^>nul ^| findstr %AppName%') do (
    set pid=%%a
    set image_name=%%b
)
if not defined pid (
    echo Process %AppName% does not exist
) else (
    echo Preparing to kill %image_name%
    echo Killing process PID: %pid%...
    taskkill /f /pid %pid%
    if %errorlevel%==0 (
        echo Process stopped successfully
    ) else (
        echo Failed to stop process
    )
)
PAUSE
GOTO menu

:restart
echo Restarting %AppName%...
call :stop
timeout /t 3 /nobreak >nul
call :start
GOTO menu

:status
echo Checking %AppName% status...
set pid=
for /f "tokens=1,2" %%a in ('jps -l 2^>nul ^| findstr %AppName%') do (
    set pid=%%a
    set image_name=%%b
)
if not defined pid (
    echo Process %AppName% is not running
) else (
    echo %image_name% is running with PID: %pid%
)
PAUSE
GOTO menu